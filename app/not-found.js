"use client";

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { motion } from 'framer-motion';

function NotFoundPage() {
  return (
    <main className="h-screen bg-app-pattern flex flex-col items-center justify-center text-center text-white px-4">
      <motion.h1
        className="text-3xl md:text-5xl lg:text-7xl font-libre font-bold"
        initial={{ y: -50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.6, ease: "easeOut" }}
      >
        Oops, Page not found!
      </motion.h1>

      <motion.div
        className="w-32 h-px bg-white my-6"
        initial={{ scaleX: 0 }}
        animate={{ scaleX: 1 }}
        transition={{ duration: 0.8, ease: "easeOut", delay: 0.3 }}
      />

      <motion.p
        className="text-base md:text-lg mb-8"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 1, ease: "easeOut", delay: 0.5 }}
      >
        But don&apos;t worry, you can head back to our{' '}
        <Link href="/" className="underline hover:opacity-80 transition-opacity">
          homepage
        </Link>
        {' '}and explore more.
      </motion.p>

      <motion.div
        initial={{ rotate: -540, scale: 0, opacity: 0 }}
        animate={{ rotate: 0, scale: 1, opacity: 1 }}
        transition={{
          duration: 1.2,
          ease: "easeOut",
          delay: 0.7
        }}
      >
        <Image
          src="/background.svg"
          alt="MoneyTales"
          width={280}
          height={280}
          className="w-full max-w-[280px]"
          priority
        />
      </motion.div>
    </main>
  );
}

export default NotFoundPage;