'use client';

import { useState, useEffect, useCallback } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { signOut } from 'firebase/auth';
import { auth } from '../../lib/firebase';
import { extractBookInformation } from '../utils/embeddingAnalysis';

export default function AlternateScenarioPage() {
  const router = useRouter();
  const [isGenerating, setIsGenerating] = useState(true);
  const [gameData, setGameData] = useState(null);
  const [error, setError] = useState('');
  const [userPrompt, setUserPrompt] = useState('');
  const [bookInfo, setBookInfo] = useState({
    bookTitle: '',
    author: '',
    changeLocation: '',
    originalEvent: ''
  });

  // Check if we're in the browser
  const [isClient, setIsClient] = useState(false);
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Function to generate the scenario for the game
  const generateScenario = useCallback(async (bookData, whatIfPrompt) => {
    try {
      // Ensure we have the required data
      if (!bookData.bookTitle || !bookData.author || !whatIfPrompt) {
        setError('Missing required information to generate scenario.');
        setIsGenerating(false);
        return;
      }

      // Generate the game data using the new API endpoint
      const response = await fetch('/api/alternate-scenario-game-data', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          bookTitle: bookData.bookTitle,
          author: bookData.author,
          changeLocation: bookData.changeLocation,
          whatIfPrompt: whatIfPrompt
        }),
      });

      const result = await response.json();

      if (response.ok && result.questions) {
        setGameData(result);
        // Store the game data in localStorage for the Godot game to access
        localStorage.setItem('storyGameData', JSON.stringify(result));
      } else {
        setError(result.error || 'Failed to generate story data');
      }
    } catch (error) {
      console.error('Error generating scenario:', error);
      setError(error.message || 'An unexpected error occurred');
    } finally {
      setIsGenerating(false);
    }
  }, []);

  // Load data from localStorage and generate scenario automatically
  useEffect(() => {
    if (!isClient) return;

    async function loadDataAndGenerateScenario() {
      try {
        // Load analysis results and user prompt from localStorage
        const savedResults = localStorage.getItem('analysisResults');
        const savedPrompt = localStorage.getItem('userPrompt');

        if (!savedResults || !savedPrompt) {
          setError('No analysis results or prompt found. Please analyze a PDF first.');
          setIsGenerating(false);
          return;
        }

        const parsedResults = JSON.parse(savedResults);
        setUserPrompt(savedPrompt);

        // Extract book information from analysis results
        const extractedBookInfo = extractBookInformation(parsedResults);
        setBookInfo(extractedBookInfo);

        // Generate the alternate scenario automatically
        await generateScenario(extractedBookInfo, savedPrompt);
      } catch (error) {
        console.error('Error loading data and generating scenario:', error);
        setError('Error loading data: ' + (error.message || 'Unknown error'));
        setIsGenerating(false);
      }
    }

    loadDataAndGenerateScenario();
  }, [isClient, generateScenario]);

  const handleLogout = async () => {
    try {
      // Call logout API to clear cookies
      await fetch('/api/auth/logout', {
        method: 'POST',
      });

      // Sign out from Firebase
      await signOut(auth);

      // Redirect to main page
      router.push('/');
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  if (!isClient) {
    return null;
  }

  return (
    <div className="min-h-screen bg-[#282A2F] text-white">
      {/* Header */}
      <header className="bg-gray-800 p-4 border-b border-gray-700">
        <div className="container mx-auto flex justify-between items-center">
          <h1 className="text-2xl font-bold">Interactive Story Game</h1>
          <div className="flex space-x-3">
            <Link
              href="/"
              className="px-4 py-2 bg-gray-700 text-white rounded-md hover:bg-gray-600 transition-colors"
            >
              Back to Analysis
            </Link>
            <button
              onClick={handleLogout}
              className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
            >
              Logout
            </button>
          </div>
        </div>
      </header>

      <main className="container mx-auto px-4 py-6">
        {/* Loading State */}
        {isGenerating && (
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
              <p className="text-lg">Generating your interactive story...</p>
              <p className="text-sm text-gray-400 mt-2">This may take a moment as we create your personalized story</p>
            </div>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="bg-red-900/50 border border-red-700 p-6 rounded-lg text-center">
            <h2 className="text-xl font-bold mb-2">Error</h2>
            <p className="mb-4">{error}</p>
            <Link
              href="/"
              className="px-6 py-3 bg-primary-green text-white rounded-md hover:bg-primary-green-dark transition-colors"
            >
              Return to PDF Analysis
            </Link>
          </div>
        )}

        {/* Godot Game Embed */}
        {!isGenerating && !error && gameData && (
          <div className="w-full h-[calc(100vh-200px)] bg-black rounded-lg overflow-hidden">
            <iframe
              src="/index.html"
              className="w-full h-full border-0"
              title="Interactive Story Game"
              allow="cross-origin-isolated"
            />
          </div>
        )}

      </main>
    </div>
  );
}
